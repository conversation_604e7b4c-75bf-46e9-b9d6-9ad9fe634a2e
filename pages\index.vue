<template>
	<view class="container">
		<!-- 顶部滚动通知 -->
		<u-notice-bar :text="noticeText" mode="horizontal" :speed="80"></u-notice-bar>

		<!-- 轮播图 -->
		<view class="swiper-box">
			<u-swiper :list="swiperList" height="300" :border-radius="10" :circular="true"></u-swiper>
		</view>

		<!-- 表单区域 -->
		<view class="form-container">
			<!-- 服务类型选择 -->
			<u-tabs :list="serviceTypes" :current="currentService" @change="onServiceChange"></u-tabs>

			<!-- 包车表单 -->
			<view v-if="currentService === 0">
				<u-form :model="form" ref="uForm" labelWidth="160rpx">
					<!-- 起点终点 -->
					<u-form-item label="起点" prop="startPoint" borderBottom>
						<view class="picker-item" @click="showStartAddressPicker">
							<text>{{ form.startPoint || '请选择起点地址' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>
					<u-form-item label="终点" prop="endPoint" borderBottom>
						<view class="picker-item" @click="showEndAddressPicker">
							<text>{{ form.endPoint || '请选择终点地址' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>

					<!-- 出发时间 -->
					<u-form-item label="出发时间" prop="timePoint" borderBottom>
						<view class="picker-item" @click="showTimePicker">
							<text>{{ form.timePoint || '请选择出发时间' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>
					<view class="hint-text">温馨提示：当天的急单建议直接联络客服下单。不退不改。</view>

					<!-- 选择车辆 -->
					<u-form-item label="选择车辆" prop="carType" borderBottom>
						<view class="picker-item" @click="showCarPicker">
							<text>{{ form.carType || '请选择车辆类型' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>

					<!-- 乘车人数 -->
					<u-form-item label="乘车人数" prop="peopleNum" borderBottom>
						<view class="">
							<u-number-box v-model="form.peopleNum" :min="1" :max="getSelectedCarMaxNum()"
								:disabled="!form.carType"></u-number-box>
						</view>
					</u-form-item>
					<view class="hint-text">温馨提示：婴儿和儿童均计入乘车人数</view>

					<!-- 联系人信息 -->
					<u-form-item label="联系人" prop="contactName" borderBottom>
						<u-input v-model="form.contactName" placeholder="请输入联系人姓名" border="none"></u-input>
					</u-form-item>
					<u-form-item label="联系方式" prop="contactPhone" borderBottom>
						<u-input v-model="form.contactPhone" placeholder="请输入手机号" border="none"></u-input>
					</u-form-item>

					<!-- 行李信息 -->
					<u-form-item label="行李件数" prop="luggageCount" borderBottom>
						<u-input v-model="form.luggageCount" type="number" placeholder="请输入行李件数"
							border="none"></u-input>
					</u-form-item>

					<!-- 证件选择 -->
					<u-form-item label="客人证件" prop="documents" borderBottom>
						<u-checkbox-group v-model="form.documents" placement="auto">
							<u-checkbox label="身份证" name="身份证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="护照" name="护照" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="港澳通行证" name="港澳通行证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="台湾通行证" name="台湾通行证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="其他" name="其他" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
						</u-checkbox-group>
					</u-form-item>

					<!-- 添加客服微信 -->
					<u-form-item borderBottom>
						<u-button @click="showQrCode">添加客服微信</u-button>
					</u-form-item>

					<!-- 备注 -->
					<u-form-item label="备注" prop="remark" borderBottom>
						<u-textarea v-model="form.remark" placeholder="请输入备注信息"></u-textarea>
					</u-form-item>
				</u-form>
			</view>

			<!-- 拼车表单 -->
			<view v-else-if="currentService === 1">
				<u-form :model="carpoolForm" ref="carpoolForm" labelWidth="160rpx">
					<!-- 起点终点 -->
					<u-form-item label="起点" prop="startPoint" borderBottom>
						<view class="picker-item" @click="showCarpoolStartAddressPicker">
							<text>{{ carpoolForm.startPoint || '请选择起点地址' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>
					<u-form-item label="终点" prop="endPoint" borderBottom>
						<view class="picker-item" @click="showCarpoolEndAddressPicker">
							<text>{{ carpoolForm.endPoint || '请选择终点地址' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>

					<!-- 出发时间 -->
					<u-form-item label="出发时间" prop="timePoint" borderBottom>
						<view class="picker-item" @click="showCarpoolTimePicker">
							<text>{{ carpoolForm.timePoint || '请选择出发时间' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>
					<view class="hint-text">温馨提示：当天的急单建议直接联络客服下单。不退不改。</view>

					<!-- 选择车辆 -->
					<u-form-item label="选择车辆" prop="carType" borderBottom>
						<view class="picker-item" @click="showCarpoolCarPicker">
							<text>{{ carpoolForm.carType || '请选择车辆类型' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>

					<!-- 乘车人数 -->
					<u-form-item label="乘车人数" prop="peopleNum" borderBottom>
						<view class="">
							<u-number-box v-model="carpoolForm.peopleNum" :min="1" :max="getSelectedCarpoolCarMaxNum()"
								:disabled="!carpoolForm.carType"></u-number-box>
						</view>
					</u-form-item>
					<view class="hint-text">温馨提示：婴儿和儿童均计入乘车人数</view>

					<!-- 联系人信息 -->
					<u-form-item label="联系人" prop="contactName" borderBottom>
						<u-input v-model="carpoolForm.contactName" placeholder="请输入联系人姓名" border="none"></u-input>
					</u-form-item>
					<u-form-item label="联系方式" prop="contactPhone" borderBottom>
						<u-input v-model="carpoolForm.contactPhone" placeholder="请输入手机号" border="none"></u-input>
					</u-form-item>

					<!-- 行李信息 -->
					<u-form-item label="行李件数" prop="luggageCount" borderBottom>
						<u-input v-model="carpoolForm.luggageCount" type="number" placeholder="请输入行李件数"
							border="none"></u-input>
					</u-form-item>

					<!-- 证件选择 -->
					<u-form-item label="客人证件" prop="documents" borderBottom>
						<u-checkbox-group v-model="carpoolForm.documents" placement="auto">
							<u-checkbox label="身份证" name="身份证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="护照" name="护照" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="港澳通行证" name="港澳通行证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="台湾通行证" name="台湾通行证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="其他" name="其他" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
						</u-checkbox-group>
					</u-form-item>

					<!-- 添加客服微信 -->
					<u-form-item borderBottom>
						<u-button @click="showQrCode">添加客服微信</u-button>
					</u-form-item>

					<!-- 备注 -->
					<u-form-item label="备注" prop="remark" borderBottom>
						<u-textarea v-model="carpoolForm.remark" placeholder="请输入备注信息"></u-textarea>
					</u-form-item>
				</u-form>
			</view>

			<!-- 接送机表单 -->
			<view v-else>
				<u-form :model="airportForm" ref="airportForm" labelWidth="160rpx">
					<!-- 接机/送机选择 -->
					<u-form-item label="服务类型" prop="serviceType" borderBottom>
						<u-radio-group v-model="airportForm.serviceType" placement="row">
							<u-radio label="接机" name="pickup"></u-radio>
							<u-radio label="送机" name="dropoff"></u-radio>
						</u-radio-group>
					</u-form-item>

					<!-- 接机：到达机场/送机：起点 -->
					<u-form-item :label="airportForm.serviceType === 'pickup' ? '到达机场' : '起点'" prop="location"
						borderBottom>
						<template v-if="airportForm.serviceType === 'pickup'">
							<view class="picker-item" @click="showAirportPicker">
								<text>{{ airportForm.airport || '请选择机场' }}</text>
								<u-icon name="arrow-right"></u-icon>
							</view>
						</template>
						<template v-else>
							<view class="picker-item" @click="showAirportStartAddressPicker">
								<text>{{ airportForm.startPoint || '请选择起点地址' }}</text>
								<u-icon name="arrow-right"></u-icon>
							</view>
						</template>
					</u-form-item>

					<!-- 接机：终点/送机：到达机场 -->
					<u-form-item :label="airportForm.serviceType === 'pickup' ? '终点' : '到达机场'" prop="destination"
						borderBottom>
						<template v-if="airportForm.serviceType === 'pickup'">
							<view class="picker-item" @click="showAirportEndAddressPicker">
								<text>{{ airportForm.endPoint || '请选择终点地址' }}</text>
								<u-icon name="arrow-right"></u-icon>
							</view>
						</template>
						<template v-else>
							<view class="picker-item" @click="showAirportPicker">
								<text>{{ airportForm.airport || '请选择机场' }}</text>
								<u-icon name="arrow-right"></u-icon>
							</view>
						</template>
					</u-form-item>

					<!-- 航班号 -->
					<u-form-item label="航班号" prop="flightNumber" borderBottom>
						<u-input v-model="airportForm.flightNumber" placeholder="请输入航班号" border="none"></u-input>
					</u-form-item>

					<!-- 接机：到达时间/送机：出发时间 -->
					<u-form-item :label="airportForm.serviceType === 'pickup' ? '到达时间' : '出发时间'" prop="time"
						borderBottom>
						<view class="picker-item" @click="showAirportTimePicker">
							<text>{{ airportForm.time || '请选择时间' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>
					<view class="hint-text">温馨提示：当天的急单建议直接联络客服下单。不退不改。</view>

					<!-- 选择车辆 -->
					<u-form-item label="选择车辆" prop="carType" borderBottom>
						<view class="picker-item" @click="showAirportCarPicker">
							<text>{{ airportForm.carType || '请选择车辆类型' }}</text>
							<u-icon name="arrow-right"></u-icon>
						</view>
					</u-form-item>

					<!-- 乘车人数 -->
					<u-form-item label="乘车人数" prop="peopleNum" borderBottom>
						<view class="">
							<u-number-box v-model="airportForm.peopleNum" :min="1" :max="getSelectedAirportCarMaxNum()"
								:disabled="!airportForm.carType"></u-number-box>
						</view>
					</u-form-item>
					<view class="hint-text">温馨提示：婴儿和儿童均计入乘车人数</view>

					<!-- 联系人信息 -->
					<u-form-item label="联系人" prop="contactName" borderBottom>
						<u-input v-model="airportForm.contactName" placeholder="请输入联系人姓名" border="none"></u-input>
					</u-form-item>
					<u-form-item label="联系方式" prop="contactPhone" borderBottom>
						<u-input v-model="airportForm.contactPhone" placeholder="请输入手机号" border="none"></u-input>
					</u-form-item>

					<!-- 行李信息 -->
					<u-form-item label="行李件数" prop="luggageCount" borderBottom>
						<u-input v-model="airportForm.luggageCount" type="number" placeholder="请输入行李件数"
							border="none"></u-input>
					</u-form-item>

					<!-- 证件选择 -->
					<u-form-item label="客人证件" prop="documents" borderBottom>
						<u-checkbox-group v-model="airportForm.documents" placement="auto">
							<u-checkbox label="身份证" name="身份证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="护照" name="护照" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="港澳通行证" name="港澳通行证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="台湾通行证" name="台湾通行证" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
							<u-checkbox label="其他" name="其他" :customStyle="{marginTop:'10rpx'}"></u-checkbox>
						</u-checkbox-group>
					</u-form-item>

					<!-- 添加客服微信 -->
					<u-form-item borderBottom>
						<u-button @click="showQrCode">添加客服微信</u-button>
					</u-form-item>

					<!-- 备注 -->
					<u-form-item label="备注" prop="remark" borderBottom>
						<u-textarea v-model="airportForm.remark" placeholder="请输入备注信息"></u-textarea>
					</u-form-item>
				</u-form>
			</view>
		</view>

		<!-- 底部价格和提交 -->
		<view class="bottom-bar">
			<view class="price">联系客服</view>
			<view style="width: 30vw;">
				<u-button type="primary" text="提交订单" @click="submitOrder"></u-button>
			</view>
		</view>

		<!-- 客服二维码弹窗 -->
		<u-modal :show="showQrCodePopup" title="客服微信" @confirm="showQrCodePopup = false"
			@close="showQrCodePopup = false">
			<view class="qr-code-popup">
				<image :show-menu-by-longpress="true" src="/static/qrcode.png" mode="aspectFit"></image>
			</view>
		</u-modal>

		<!-- 时间选择器弹窗 -->
		<u-datetime-picker :show="showTimePickerVisible" v-model="currentDate" mode="datetime" @confirm="onTimeConfirm"
			@cancel="showTimePickerVisible = false"></u-datetime-picker>

		<!-- 车辆选择器弹窗 -->
		<u-picker :show="showCarPickerVisible" :columns="[carList]" keyName="carName" @confirm="onCarConfirm"
			@cancel="showCarPickerVisible = false"></u-picker>

		<!-- 机场选择器弹窗 -->
		<u-picker :show="showAirportPickerVisible" :columns="[airportList]" @confirm="onAirportConfirm"
			@cancel="showAirportPickerVisible = false"></u-picker>

		<!-- 机场时间选择器弹窗 -->
		<u-datetime-picker :show="showAirportTimePickerVisible" v-model="currentDate" mode="datetime"
			@confirm="onAirportTimeConfirm" @cancel="showAirportTimePickerVisible = false"></u-datetime-picker>

		<!-- 机场车辆选择器弹窗 -->
		<u-picker :show="showAirportCarPickerVisible" :columns="[carList]" @confirm="onAirportCarConfirm"
			@cancel="showAirportCarPickerVisible = false"></u-picker>

		<!-- 拼车时间选择器弹窗 -->
		<u-datetime-picker :show="showCarpoolTimePickerVisible" v-model="currentDate" mode="datetime"
			@confirm="onCarpoolTimeConfirm" @cancel="showCarpoolTimePickerVisible = false"></u-datetime-picker>

		<!-- 拼车车辆选择器弹窗 -->
		<u-picker :show="showCarpoolCarPickerVisible" :columns="[carList]" keyName="carName" @confirm="onCarpoolCarConfirm"
			@cancel="showCarpoolCarPickerVisible = false"></u-picker>

		<!-- 包车起点选择器弹窗 -->
		<u-picker :show="showStartAddressPickerVisible" :columns="[startAddressList]" @confirm="onStartAddressConfirm"
			@cancel="showStartAddressPickerVisible = false"></u-picker>

		<!-- 包车终点选择器弹窗 -->
		<u-picker :show="showEndAddressPickerVisible" :columns="[endAddressList]" @confirm="onEndAddressConfirm"
			@cancel="showEndAddressPickerVisible = false"></u-picker>

		<!-- 拼车起点选择器弹窗 -->
		<u-picker :show="showCarpoolStartAddressPickerVisible" :columns="[startAddressList]" @confirm="onCarpoolStartAddressConfirm"
			@cancel="showCarpoolStartAddressPickerVisible = false"></u-picker>

		<!-- 拼车终点选择器弹窗 -->
		<u-picker :show="showCarpoolEndAddressPickerVisible" :columns="[endAddressList]" @confirm="onCarpoolEndAddressConfirm"
			@cancel="showCarpoolEndAddressPickerVisible = false"></u-picker>

		<!-- 接送机起点选择器弹窗 -->
		<u-picker :show="showAirportStartAddressPickerVisible" :columns="[startAddressList]" @confirm="onAirportStartAddressConfirm"
			@cancel="showAirportStartAddressPickerVisible = false"></u-picker>

		<!-- 接送机终点选择器弹窗 -->
		<u-picker :show="showAirportEndAddressPickerVisible" :columns="[endAddressList]" @confirm="onAirportEndAddressConfirm"
			@cancel="showAirportEndAddressPickerVisible = false"></u-picker>
	</view>
</template>

<script>
	import {
		listCarType
	} from "@/api/lease/carType.js"
	import {
		baseUrl
	} from "../config";
	import {
		listSwiper
	} from "@/api/lease/swiper.js"
	import {
		addCarOrder
	} from "@/api/lease/carOrder.js"
	import { listAirport } from "@/api/lease/airport.js"
	import { listStarAddress } from "@/api/lease/starAddress.js"
	import { listEndAddress } from "@/api/lease/endAddress.js"
	export default {
		data() {
			return {
				currentDate: Number(new Date()),
				baseUrl,
				noticeText: "欢迎使用租车服务，祝您旅途愉快！",
				swiperList: [], // 轮播图数据
				serviceTypes: [{
						name: '包车'
					},
					{
						name: '拼车'
					},
					{
						name: '接送机'
					}
				],
				currentService: 0,
				carList: [],
				airportList: [],
				startAddressList: [], // 起点地址列表
				endAddressList: [], // 终点地址列表
				showStartAddressPickerVisible: false, // 起点选择器显示状态
				showEndAddressPickerVisible: false, // 终点选择器显示状态
				showCarpoolStartAddressPickerVisible: false, // 拼车起点选择器显示状态
				showCarpoolEndAddressPickerVisible: false, // 拼车终点选择器显示状态
				showAirportStartAddressPickerVisible: false, // 接送机起点选择器显示状态
				showAirportEndAddressPickerVisible: false, // 接送机终点选择器显示状态
				form: {
					startPoint: '',
					startPointName: '',
					endPoint: '',
					endPointName: '',
					timePoint: '',
					carType: '',
					peopleNum: 1,
					contactName: '',
					contactPhone: '',
					luggageCount: '',
					documents: [],
					remark: '',
					lineId: null
				},
				showQrCodePopup: false,
				showTimePickerVisible: false,
				showCarPickerVisible: false,
				showAirportPickerVisible: false,
				showAirportTimePickerVisible: false,
				showAirportCarPickerVisible: false,
				showCarpoolTimePickerVisible: false,
				showCarpoolCarPickerVisible: false,
				carpoolForm: {
					startPoint: '',
					startPointName: '',
					endPoint: '',
					endPointName: '',
					timePoint: '',
					carType: '',
					peopleNum: 1,
					contactName: '',
					contactPhone: '',
					luggageCount: '',
					documents: [],
					remark: '',
					lineId: null
				},
				airportForm: {
					serviceType: 'pickup',
					airport: '',
					startPoint: '',
					endPoint: '',
					flightNumber: '',
					time: '',
					carType: '',
					peopleNum: 1,
					contactName: '',
					contactPhone: '',
					luggageCount: '',
					documents: [],
					remark: '',
					lineId: null
				}
			}
		},
		onLoad() {
			this.getSwiperList()
			this.getCarTypeList()
			this.getAirportList()
			this.getStartAddressList()
			this.getEndAddressList()
		},
		onShow() {},
		methods: {
			getAirportList(){
				listAirport({pageSize:100000}).then(res=>{
					this.airportList = res.rows.map(item=>item.airportName)
				})
			},
			// 获取起点地址列表
			getStartAddressList() {
				listStarAddress({pageSize: 1000000}).then(res => {
					this.startAddressList = this.buildAddressTree(res.rows)
				})
			},
			// 获取终点地址列表
			getEndAddressList() {
				listEndAddress({pageSize: 1000000}).then(res => {
					this.endAddressList = this.buildAddressTree(res.rows)
				})
			},
			// 构建地址树结构
			buildAddressTree(data) {
				const tree = []
				const map = {}

				// 先创建所有节点的映射
				data.forEach(item => {
					map[item.addrId] = {
						...item,
						children: []
					}
				})

				// 构建树结构
				data.forEach(item => {
					if (item.parentId === 0) {
						// 一级节点
						tree.push(map[item.addrId])
					} else {
						// 二级节点
						if (map[item.parentId]) {
							map[item.parentId].children.push(map[item.addrId])
						}
					}
				})

				// 转换为选择器需要的格式
				const result = []
				tree.forEach(parent => {
					if (parent.children && parent.children.length > 0) {
						parent.children.forEach(child => {
							result.push(`${parent.addrName}-${child.addrName}`)
						})
					} else {
						result.push(parent.addrName)
					}
				})

				return result
			},
			onServiceChange(e) {
				this.currentService = e.index;
			},
			getCarTypeList() {
				listCarType({
					pageSize: 100000,
					status: 1
				}).then(res => {
					this.carList = res.rows
				})
			},
			getSwiperList() {
				listSwiper({
					pageSize: 100000
				}).then(res => {
					this.swiperList = res.rows.map(item => this.baseUrl + item.pic)
				})
			},
			showTimePicker() {
				this.showTimePickerVisible = true;
			},
			showCarPicker() {
				this.showCarPickerVisible = true;
			},
			onTimeConfirm(e) {
				// 将时间戳转换为 yyyy-MM-dd HH:mm:ss 格式
				const date = new Date(e.value);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');
				this.form.timePoint = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
				this.showTimePickerVisible = false;
			},
			onCarConfirm(e) {
				this.form.carType = e.value[0].carName;
				// 如果当前人数超过最大限制，则重置为1
				if (this.form.peopleNum > e.value[0].maxNum) {
					this.form.peopleNum = 1;
				}
				this.showCarPickerVisible = false;
			},
			showQrCode() {
				this.showQrCodePopup = true;
			},
			submitOrder() {
				uni.showLoading({
					title:"请稍后……",
					mask:true
				})
				// 对包车和接送机表单项进行非空校验
				const phoneReg = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/;
				if (this.currentService === 0) {
					const f = this.form;
					if (!f.startPoint) {
						uni.showToast({
							title: '请输入起点地址',
							icon: 'none'
						});
						return;
					}
					if (!f.endPoint) {
						uni.showToast({
							title: '请输入终点地址',
							icon: 'none'
						});
						return;
					}
					if (!f.timePoint) {
						uni.showToast({
							title: '请选择出发时间',
							icon: 'none'
						});
						return;
					}
					if (!f.carType) {
						uni.showToast({
							title: '请选择车辆类型',
							icon: 'none'
						});
						return;
					}
					if (!f.peopleNum) {
						uni.showToast({
							title: '请输入乘车人数',
							icon: 'none'
						});
						return;
					}
					if (!f.contactName) {
						uni.showToast({
							title: '请输入联系人姓名',
							icon: 'none'
						});
						return;
					}
					if (!f.contactPhone) {
						uni.showToast({
							title: '请输入联系方式',
							icon: 'none'
						});
						return;
					}
					if (!phoneReg.test(f.contactPhone)) {
						uni.showToast({
							title: '请输入正确的手机号',
							icon: 'none'
						});
						return;
					}
					if (!f.luggageCount) {
						uni.showToast({
							title: '请输入行李件数',
							icon: 'none'
						});
						return;
					}
					if (!f.documents || f.documents.length === 0) {
						uni.showToast({
							title: '请选择证件类型',
							icon: 'none'
						});
						return;
					}
					const params = {
						serviceType: 'charter',
						startPoint: f.startPoint,
						endPoint: f.endPoint,
						timePoint: f.timePoint,
						carType: f.carType,
						peopleNum: f.peopleNum,
						contactName: f.contactName,
						contactPhone: f.contactPhone,
						luggageCount: f.luggageCount,
						documents: f.documents.join(','),
						remark: f.remark,
						startPointName: f.startPointName,
						endPointName: f.endPointName,
						lineId: f.lineId
					};
					// 调用包车下单接口
					addCarOrder(params)
						.then((orderRes) => {
							uni.hideLoading();
							// 清空表单
							this.clearCharterForm();
							// 弹出跳转确认弹框
							uni.showModal({
								title: '提交成功',
								content: '包车订单提交成功，是否跳转到订单页面查看？',
								confirmText: '是',
								cancelText: '否',
								success: (res) => {
									if (res.confirm) {
										// 用户点击"是"，跳转到订单页面
										uni.navigateTo({
											url: '/pages/mine/carOrder/carOrder'
										});
									}
									// 用户点击"否"或取消，保留在首页，不做任何操作
								}
							});
						})
						.catch(() => {
							uni.hideLoading();
							uni.showToast({
								title: '包车订单提交失败，请重试',
								icon: 'none'
							});
						});
				} else if (this.currentService === 1) {
					// 拼车表单验证
					const c = this.carpoolForm;
					if (!c.startPoint) {
						uni.showToast({
							title: '请输入起点地址',
							icon: 'none'
						});
						return;
					}
					if (!c.endPoint) {
						uni.showToast({
							title: '请输入终点地址',
							icon: 'none'
						});
						return;
					}
					if (!c.timePoint) {
						uni.showToast({
							title: '请选择出发时间',
							icon: 'none'
						});
						return;
					}
					if (!c.carType) {
						uni.showToast({
							title: '请选择车辆类型',
							icon: 'none'
						});
						return;
					}
					if (!c.peopleNum) {
						uni.showToast({
							title: '请输入乘车人数',
							icon: 'none'
						});
						return;
					}
					if (!c.contactName) {
						uni.showToast({
							title: '请输入联系人姓名',
							icon: 'none'
						});
						return;
					}
					if (!c.contactPhone) {
						uni.showToast({
							title: '请输入联系方式',
							icon: 'none'
						});
						return;
					}
					if (!phoneReg.test(c.contactPhone)) {
						uni.showToast({
							title: '请输入正确的手机号',
							icon: 'none'
						});
						return;
					}
					if (!c.luggageCount) {
						uni.showToast({
							title: '请输入行李件数',
							icon: 'none'
						});
						return;
					}
					if (!c.documents || c.documents.length === 0) {
						uni.showToast({
							title: '请选择证件类型',
							icon: 'none'
						});
						return;
					}
					const params = {
						serviceType: 'carpooling',
						startPoint: c.startPoint,
						endPoint: c.endPoint,
						timePoint: c.timePoint,
						carType: c.carType,
						peopleNum: c.peopleNum,
						contactName: c.contactName,
						contactPhone: c.contactPhone,
						luggageCount: c.luggageCount,
						documents: c.documents.join(','),
						remark: c.remark,
						startPointName: c.startPointName,
						endPointName: c.endPointName,
						lineId: c.lineId
					};
					// 调用拼车下单接口
					addCarOrder(params)
						.then((orderRes) => {
							uni.hideLoading();
							// 清空表单
							this.clearCarpoolForm();
							// 弹出跳转确认弹框
							uni.showModal({
								title: '提交成功',
								content: '拼车订单提交成功，是否跳转到订单页面查看？',
								confirmText: '是',
								cancelText: '否',
								success: (res) => {
									if (res.confirm) {
										// 用户点击"是"，跳转到订单页面
										uni.navigateTo({
											url: '/pages/mine/carOrder/carOrder'
										});
									}
									// 用户点击"否"或取消，保留在首页，不做任何操作
								}
							});
						})
						.catch(() => {
							uni.hideLoading();
							uni.showToast({
								title: '拼车订单提交失败，请重试',
								icon: 'none'
							});
						});
				} else {
					const a = this.airportForm;
					if (!a.serviceType) {
						uni.showToast({
							title: '请选择服务类型',
							icon: 'none'
						});
						return;
					}
					if (a.serviceType === 'pickup') {
						if (!a.airport) {
							uni.showToast({
								title: '请选择到达机场',
								icon: 'none'
							});
							return;
						}
						if (!a.endPoint) {
							uni.showToast({
								title: '请输入终点地址',
								icon: 'none'
							});
							return;
						}
					} else {
						if (!a.startPoint) {
							uni.showToast({
								title: '请输入起点地址',
								icon: 'none'
							});
							return;
						}
						if (!a.airport) {
							uni.showToast({
								title: '请选择到达机场',
								icon: 'none'
							});
							return;
						}
					}
					if (!a.flightNumber) {
						uni.showToast({
							title: '请输入航班号',
							icon: 'none'
						});
						return;
					}
					if (!a.time) {
						uni.showToast({
							title: a.serviceType === 'pickup' ? '请选择到达时间' : '请选择出发时间',
							icon: 'none'
						});
						return;
					}
					if (!a.carType) {
						uni.showToast({
							title: '请选择车辆类型',
							icon: 'none'
						});
						return;
					}
					if (!a.contactName) {
						uni.showToast({
							title: '请输入联系人姓名',
							icon: 'none'
						});
						return;
					}
					if (!a.contactPhone) {
						uni.showToast({
							title: '请输入联系方式',
							icon: 'none'
						});
						return;
					}
					if (!phoneReg.test(a.contactPhone)) {
						uni.showToast({
							title: '请输入正确的手机号',
							icon: 'none'
						});
						return;
					}
					if (!a.luggageCount) {
						uni.showToast({
							title: '请输入行李件数',
							icon: 'none'
						});
						return;
					}
					if (!a.documents || a.documents.length === 0) {
						uni.showToast({
							title: '请选择证件类型',
							icon: 'none'
						});
						return;
					}
					if (!a.remark) {
						uni.showToast({
							title: '请输入备注信息',
							icon: 'none'
						});
						return;
					}
					const params = {
						serviceType: a.serviceType,
						airport: a.airport,
						startPoint: a.startPoint,
						endPoint: a.endPoint,
						flightNumber: a.flightNumber,
						time: a.time,
						carType: a.carType,
						peopleNum: a.peopleNum,
						contactName: a.contactName,
						contactPhone: a.contactPhone,
						luggageCount: a.luggageCount,
						documents: a.documents.join(','),
						remark: a.remark,
						startLatitude: a.startLatitude,
						startLongitude: a.startLongitude,
						endLatitude: a.endLatitude,
						endLongitude: a.endLongitude,
						lineId: a.lineId
					};
					uni.hideLoading()
					uni.showToast({
						title:"开发中",
						icon:"none"
					})
					// // 调用接送机下单接口
					// addCarOrder(params)
					// 	.then(() => {
					// 		uni.showToast({
					// 			title: '接送机订单提交成功',
					// 			icon: 'success'
					// 		});
					// 		// 可在此清空表单或跳转页面
					// 	})
					// 	.catch(() => {
					// 		uni.showToast({
					// 			title: '接送机订单提交失败，请重试',
					// 			icon: 'none'
					// 		});
					// 	});
				}
			},
			showAirportPicker() {
				this.showAirportPickerVisible = true;
			},
			showAirportTimePicker() {
				this.showAirportTimePickerVisible = true;
			},
			showAirportCarPicker() {
				this.showAirportCarPickerVisible = true;
			},
			onAirportConfirm(e) {
				this.airportForm.airport = e.value[0];
				this.showAirportPickerVisible = false;
			},
			onAirportTimeConfirm(e) {
				const date = new Date(e.value);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');
				this.airportForm.time = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
				this.showAirportTimePickerVisible = false;
			},
			onAirportCarConfirm(e) {
				this.airportForm.carType = e.value[0].carName;
				// 如果当前人数超过最大限制，则重置为1
				if (this.airportForm.peopleNum > e.value[0].maxNum) {
					this.airportForm.peopleNum = 1;
				}
				this.showAirportCarPickerVisible = false;
			},
			// 拼车相关方法
			showCarpoolTimePicker() {
				this.showCarpoolTimePickerVisible = true;
			},
			showCarpoolCarPicker() {
				this.showCarpoolCarPickerVisible = true;
			},
			onCarpoolTimeConfirm(e) {
				const date = new Date(e.value);
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				const hours = String(date.getHours()).padStart(2, '0');
				const minutes = String(date.getMinutes()).padStart(2, '0');
				const seconds = String(date.getSeconds()).padStart(2, '0');
				this.carpoolForm.timePoint = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
				this.showCarpoolTimePickerVisible = false;
			},
			onCarpoolCarConfirm(e) {
				this.carpoolForm.carType = e.value[0].carName;
				// 如果当前人数超过最大限制，则重置为1
				if (this.carpoolForm.peopleNum > e.value[0].maxNum) {
					this.carpoolForm.peopleNum = 1;
				}
				this.showCarpoolCarPickerVisible = false;
			},
			submitAirportOrder() {
				// 手机号验证
				const phoneReg = /^1[3-9]\d{9}$/;
				if (!phoneReg.test(this.airportForm.contactPhone)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				// TODO: 提交接送机订单逻辑
			},
			// 显示包车起点选择器
			showStartAddressPicker() {
				this.showStartAddressPickerVisible = true;
			},
			// 显示包车终点选择器
			showEndAddressPicker() {
				this.showEndAddressPickerVisible = true;
			},
			// 包车起点选择确认
			onStartAddressConfirm(e) {
				this.form.startPoint = e.value[0];
				this.form.startPointName = e.value[0];
				this.showStartAddressPickerVisible = false;
			},
			// 包车终点选择确认
			onEndAddressConfirm(e) {
				this.form.endPoint = e.value[0];
				this.form.endPointName = e.value[0];
				this.showEndAddressPickerVisible = false;
			},
			// 显示拼车起点选择器
			showCarpoolStartAddressPicker() {
				this.showCarpoolStartAddressPickerVisible = true;
			},
			// 显示拼车终点选择器
			showCarpoolEndAddressPicker() {
				this.showCarpoolEndAddressPickerVisible = true;
			},
			// 拼车起点选择确认
			onCarpoolStartAddressConfirm(e) {
				this.carpoolForm.startPoint = e.value[0];
				this.carpoolForm.startPointName = e.value[0];
				this.showCarpoolStartAddressPickerVisible = false;
			},
			// 拼车终点选择确认
			onCarpoolEndAddressConfirm(e) {
				this.carpoolForm.endPoint = e.value[0];
				this.carpoolForm.endPointName = e.value[0];
				this.showCarpoolEndAddressPickerVisible = false;
			},
			// 显示接送机起点选择器
			showAirportStartAddressPicker() {
				this.showAirportStartAddressPickerVisible = true;
			},
			// 显示接送机终点选择器
			showAirportEndAddressPicker() {
				this.showAirportEndAddressPickerVisible = true;
			},
			// 接送机起点选择确认
			onAirportStartAddressConfirm(e) {
				this.airportForm.startPoint = e.value[0];
				this.airportForm.startPointName = e.value[0];
				this.showAirportStartAddressPickerVisible = false;
			},
			// 接送机终点选择确认
			onAirportEndAddressConfirm(e) {
				this.airportForm.endPoint = e.value[0];
				this.airportForm.endPointName = e.value[0];
				this.showAirportEndAddressPickerVisible = false;
			},
			getSelectedCarMaxNum() {
				if (!this.form.carType) return 1;
				const selectedCar = this.carList.find(car => car.carName === this.form.carType);
				return selectedCar ? selectedCar.maxNum : 1;
			},
			getSelectedAirportCarMaxNum() {
				if (!this.airportForm.carType) return 1;
				const selectedCar = this.carList.find(car => car.carName === this.airportForm.carType);
				return selectedCar ? selectedCar.maxNum : 1;
			},
			getSelectedCarpoolCarMaxNum() {
				if (!this.carpoolForm.carType) return 1;
				const selectedCar = this.carList.find(car => car.carName === this.carpoolForm.carType);
				return selectedCar ? selectedCar.maxNum : 1;
			},

			// 清空包车表单
			clearCharterForm() {
				this.form = {
					startPoint: '',
					startPointName: '',
					endPoint: '',
					endPointName: '',
					timePoint: '',
					carType: '',
					peopleNum: 1,
					contactName: '',
					contactPhone: '',
					luggageCount: '',
					documents: [],
					remark: '',
					lineId: null
				};
			},
			// 清空拼车表单
			clearCarpoolForm() {
				this.carpoolForm = {
					startPoint: '',
					startPointName: '',
					endPoint: '',
					endPointName: '',
					timePoint: '',
					carType: '',
					peopleNum: 1,
					contactName: '',
					contactPhone: '',
					luggageCount: '',
					documents: [],
					remark: '',
					lineId: null
				};
			},
			// 清空接送机表单
			clearAirportForm() {
				this.airportForm = {
					serviceType: 'pickup',
					airport: '',
					startPoint: '',
					endPoint: '',
					flightNumber: '',
					time: '',
					carType: '',
					peopleNum: 1,
					contactName: '',
					contactPhone: '',
					luggageCount: '',
					documents: [],
					remark: '',
					lineId: null
				};
			}
		}
	}
</script>


<style lang="scss">
	.container {
		padding: 20rpx;
		padding-bottom: 10vh;
	}

	.swiper-box {
		margin: 20rpx 0;
	}

	.form-container {
		background-color: #fff;
		border-radius: 10rpx;
		padding: 20rpx;
		margin-top: 20rpx;

		:deep(.u-form-item) {
			.u-form-item__body {
				.u-form-item__body__left {
					min-width: 160rpx; // 增加label宽度
				}
			}
		}
	}

	.picker-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 0;

		text {
			color: #333;
			font-size: 28rpx;
		}
	}

	.bottom-bar {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 40rpx;
		background-color: #fff;
		box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
		z-index: 999;

		.price {
			font-size: 32rpx;
			color: #f56c6c;
			font-weight: bold;
		}

		.submit-btn {
			margin-left: auto; // 按钮自动靠右
			width: 25vw;
			height: 80rpx;
			background-color: #007AFF;
			color: #ffffff;
			border: none;
			border-radius: 10rpx;
			font-size: 32rpx;
			text-align: center;
			line-height: 80rpx;
		}
	}

	.qr-code-popup {
		padding: 30rpx;
		display: flex;
		justify-content: center;
		align-items: center;

		image {
			width: 400rpx;
			height: 400rpx;
		}
	}

	// 证件多选框样式
	:deep(.u-checkbox-group) {
		display: flex;
		flex-wrap: wrap;
		gap: 20rpx;
	}

	.product-entry {
		margin: 20rpx 0;
		text-align: center;
	}

	// 添加提示文字样式
	.hint-text {
		font-size: 24rpx;
		color: #999;
		margin: 10rpx 0;
	}
</style>